diff a/src/app/domain/whitelabel.service.ts b/src/app/domain/whitelabel.service.ts	(rejected hunks)
@@ -154,16 +197,8 @@ export class WhitelabelService {
   }
 
   getApiAddress() {
-    const apiParam = (new URLSearchParams(window.location.search)).get("apiUrl");
-    if (apiParam && !environment.production) {
-      try {
-        const url = new URL(apiParam);
-        if (url.protocol === "https:" && url.hostname === "vea-api-staging.vretta.com") {
-          console.info("Using API address from query parameter:", apiParam);
-          return apiParam;
-        }
-      } catch {}
-      console.warn("Invalid API address provided in query parameter:", apiParam);
+    if (!environment.production && apiUrlOverride) {
+      return apiUrlOverride;
     }
     return this.contextData.apiAddress(window.location.hostname);
   }
